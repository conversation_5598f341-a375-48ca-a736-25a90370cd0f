package com.bilibili.miniapp.open.repository.redis;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 原则：
 * </p>
 * <p>1.使用_作为分隔符</p>
 * <p>2.前缀在前，值在后</p>
 *
 * <AUTHOR>
 * @date 2024/12/09 22:23
 */
@Getter
@AllArgsConstructor
public enum RedisKeyPattern {
    //mp=miniapp
    APP_SEASON_AUTHORIZATION("mp_open_app_season_auth_%s", "小程序关联的剧id（被授权的）"),
    APP_AUTHOR_AUTHORIZATION("mp_open_app_author_auth_%s", "小程序关联的创作者（被授权的）"),
    APP_SEASON_ADD_TAB("app_season_add_tab_%s", "小程序添加剧到tab锁"),
    API_ACCESS_KEY_TOKEN("mp_open_api_access_key_%s", "开放平台OpenApi AccessKey"),

    API_USER_ACCESS("mp_open_api_user_access_%s", "用户访问过的小程序列表"),
    //通用
    OPEN_LOCK_KEY_PREFIX("mp_open_lock_", "开放平台分布式锁前缀"),

    OPEN_LOCK_TEMPLATE_MINI_APP_PUBLISH("mp_open_lock_template_mini_app_publish", "开放平台积木自动发布分布式锁"),
    OPEN_LOCK_ACCESS_CREATION(OPEN_LOCK_KEY_PREFIX.getPattern() + "access_create_%s", "创建token"),

    OPEN_LOCK_COMPANY_ADMISSION_CREATION(OPEN_LOCK_KEY_PREFIX.getPattern() + "company_admission_create_%s", "创建企业准入的分布式锁"),
    OPEN_LOCK_COMPANY_AUDIT(OPEN_LOCK_KEY_PREFIX.getPattern() + "company_audit_%s", "审核企业的分布式锁"),

    OPEN_LOCK_APP_ADMISSION_CREATION(OPEN_LOCK_KEY_PREFIX.getPattern() + "app_admission_create_%s", "创建小程序准入的分布式锁"),
    OPEN_LOCK_APP_AUDIT(OPEN_LOCK_KEY_PREFIX.getPattern() + "app_audit_%s", "审核小程序的分布式锁"),
    OPEN_LOCK_APP_CUSTOM_LINK(OPEN_LOCK_KEY_PREFIX.getPattern() + "app_config_custom_link_%s", "小程序自定义路径参数的分布式锁"),


    OPEN_LOCK_ORDER_CREATION(OPEN_LOCK_KEY_PREFIX.getPattern() + "order_create_%s_%s", "订单创建分布式锁"),
    //更新和创建不会同时发生，因此分布式锁是分开的
    OPEN_LOCK_ORDER_UPDATE(OPEN_LOCK_KEY_PREFIX.getPattern() + "order_update_%d", "订单更新分布式锁"),
    OPEN_LOCK_ORDER_ID_SEGMENT(OPEN_LOCK_KEY_PREFIX.getPattern() + "order_segment", "订单id号段分配分布式锁"),

    //支付处理中{order_id}，包括exchange、confirm、notify阶段
    OPEN_LOCK_PAY_PROGRESS(OPEN_LOCK_KEY_PREFIX.getPattern() + "pay_progress_%d", "支付处理分布式锁"),
    //
    OPEN_ORDER_ID("{mp_open_order_id}", "订单id"),
    OPEN_ORDER_ID_MAX("{mp_open_order_id}_max", "订单id在当前号段内的最大id"),

    OPEN_LOCK_USER_ACCESS(OPEN_LOCK_KEY_PREFIX.getPattern() + "user_access_%d", "用户访问记录分布式锁"),
    APP_SEASON_APPLET_AUTHORIZATION("mp_open_app_season_auth_applet_%s", "剧关联的小程序id（被授权的）"),

    OPEN_COMMENT_YOUKU_SHOW_ID_GET_SHOW_NAME("open_comment_youku_show_id_get_show_name_%s", "获取有酷视频剧的show_id对应的剧名"),

    OPEN_COMMENT_YOUKU_VIDEO_ID_GET_NAME("open_comment_youku_video_id_get_show_name_%s", "获取有酷视频剧的video_id对应的剧名"),

    // 1.mid 2.appId
    OPEN_USER_PRE_AUTH_INFO("mp_open_user_pre_auth_info_%s_%s", "用户预授权信息"),
    OPEN_LOCK_USER_PRE_AUTH_INFO(OPEN_LOCK_KEY_PREFIX.getPattern() + "user_pre_auth_info_%s_%s", "用户预授权信息分布式锁"),

    OPEN_APP_CREATING("open_app_creating_%d_%s", "小程序创建中key"),
    OPEN_APP_CUSTOM_LINK("open_app_custom_link_%s", "小程序自定义链接"),

    CLIENT_APP_GET_UNLOCK_EPS("client_app_get_unlock_eps_%s_%s_%d", "小程序c端获取用户解锁ep(appid+openId+seasonId)"),
    CLIENT_APP_USER_UNLOCK_EPS("client_app_user_unlock_eps_%s_%s_%d","小程序c端用户解锁ep分布式锁(appid+openId+seasonId)"),

    OPEN_PLATFORM_ICP_CONFIG_KET("open_platform_icp_config_key", "小程序备案基础信息key"),

    ECPM_EVENT_LOG_POSITION("ecpm_event_log_{}_{}_{}_{}","ecpm位置记录key(appId, openId, queryTime[YYYY-MM-dd[ HH]], page*pageSize)")

    ;
    private final String pattern;
    private final String desc;

    public static String getUserPreAuthInfoKey(Long mid, String appId) {
        return String.format(OPEN_USER_PRE_AUTH_INFO.getPattern(), mid, appId);
    }

    public static String buildAppSeasonAppletKey(Long seasonId) {
        return String.format(RedisKeyPattern.APP_SEASON_APPLET_AUTHORIZATION.getPattern(), seasonId);
    }
}

package com.bilibili.miniapp.open.service.contract;

import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.contract.impl.ContractService;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractCreateBo;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractRequest;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractResult;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 合同结算服务测试
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@ExtendWith(MockitoExtension.class)
public class ContractSettlementServiceTest {

    @Mock
    private IAccountService accountService;

    @Mock
    private ContractCenterApiService contractCenterApiService;

    @Mock
    private MiniAppOpenContractDao miniAppOpenContractDao;

    @InjectMocks
    private ContractService contractSettlementService;

    private SettlementContractCreateBo testRequest;
    private final Long TEST_MID = 123456L;
    private final String TEST_APP_ID = "bili123456789";

    @BeforeEach
    void setUp() {
        testRequest = SettlementContractCreateBo.builder()
                .appId(TEST_APP_ID)
                .signatoryName("测试用户")
                .signatoryPhone("***********")
                .signatoryEmail("<EMAIL>")
                .contactAddress("测试地址")
                .contractStartTime(System.currentTimeMillis() / 1000)
                .contractEndTime(System.currentTimeMillis() / 1000 + 86400 * 365) // 一年后
                .build();
    }

    @Test
    void whenUserHasSuperAdminPermission_ThenCreateContractSuccessfully() {
        // Mock超管权限
        when(accountService.getUserRole(TEST_MID, TEST_APP_ID))
                .thenReturn(MiniAppPermission.SUPER_ADMIN.getCode());

        // Mock合同中心API调用
        CreateContractResult mockResult = new CreateContractResult();
        mockResult.setContractId("contract_123");
        mockResult.setState(1);
        when(contractCenterApiService.createContract(any(CreateContractRequest.class)))
                .thenReturn(mockResult);

        // Mock数据库插入
        when(miniAppOpenContractDao.insert(any(MiniAppOpenContractPo.class)))
                .thenReturn(1);

        // 执行测试
        contractSettlementService.createSettlementContract(TEST_MID, testRequest);

        // 验证调用
        verify(accountService).getUserRole(TEST_MID, TEST_APP_ID);
        verify(contractCenterApiService).createContract(any(CreateContractRequest.class));
        verify(miniAppOpenContractDao).insert(any(MiniAppOpenContractPo.class));
    }

    @Test
    void whenUserHasNoSuperAdminPermission_ThenThrowException() {
        // Mock非超管权限
        when(accountService.getUserRole(TEST_MID, TEST_APP_ID))
                .thenReturn(MiniAppPermission.DEVELOPER.getCode());

        // 执行并断言异常
        assertThrows(ServiceException.class,
                () -> contractSettlementService.createSettlementContract(TEST_MID, testRequest));

        // 验证只调用了权限检查，没有调用其他服务
        verify(accountService).getUserRole(TEST_MID, TEST_APP_ID);
        verify(contractCenterApiService, never()).createContract(any());
        verify(miniAppOpenContractDao, never()).insert(any());
    }

    @Test
    void whenContractTimeInvalid_ThenThrowException() {
        // Mock超管权限
        when(accountService.getUserRole(TEST_MID, TEST_APP_ID))
                .thenReturn(MiniAppPermission.SUPER_ADMIN.getCode());

        // 设置无效的时间（结束时间早于开始时间）
        testRequest.setContractEndTime(testRequest.getContractStartTime() - 1000);

        // 执行并断言异常
        assertThrows(ServiceException.class,
                () -> contractSettlementService.createSettlementContract(TEST_MID, testRequest));

        // 验证没有调用合同中心API
        verify(contractCenterApiService, never()).createContract(any());
    }
}

package com.bilibili.miniapp.open.service.rpc.http.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 签约方实体
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractEntity {
    
    /**
     * 实体合作伙伴
     */
    private String entityPartner;
    
    /**
     * 实体类型
     */
    private Integer entityType;
    
    /**
     * 实体名称
     */
    private String entityName;
    
    /**
     * 申请ID
     */
    private String applyId;
    
    /**
     * 供应商ID
     */
    private String supplierId;
    
    /**
     * 是否填写人
     */
    private Integer isFiller;
    
    /**
     * 通知邮箱
     */
    private String notifyEmail;
    
    /**
     * 印章类型
     */
    private String sealType;
    
    /**
     * 通知mid
     */
    private Long notifyMid;
    
    /**
     * 贡献类型
     */
    private String contributionType;
    
    /**
     * UUID
     */
    private String uuid;
    
    /**
     * 贡献比例
     */
    private Integer contributionRatio;
    
    /**
     * 实体mid
     */
    private Long entityMid;
    
    /**
     * 实体身份
     */
    private String entityIdentity;
    
    /**
     * 是否检查mid
     */
    private Integer isCheckMid;
    
    /**
     * 通知手机号
     */
    private String notifyMobile;
}

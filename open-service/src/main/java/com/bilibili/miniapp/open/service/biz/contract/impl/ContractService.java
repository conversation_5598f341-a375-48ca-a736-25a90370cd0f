package com.bilibili.miniapp.open.service.biz.contract.impl;

import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractCreateBo;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.enums.ContractCenterContractType;
import com.bilibili.miniapp.open.service.enums.ContractCenterEntityType;
import com.bilibili.miniapp.open.service.rpc.http.dto.ContractEntity;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractRequest;
import com.bilibili.miniapp.open.service.rpc.http.dto.CreateContractResult;
import com.bilibili.miniapp.open.service.rpc.http.dto.OaInfo;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

/**
 * 合同结算服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class ContractService implements IContractService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private ContractCenterApiService contractCenterApiService;

    @Autowired
    private MiniAppOpenContractDao miniAppOpenContractDao;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private ConfigCenter configCenter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LockRequest(key = "'create_settlement_contract_' + #contractCreateBo.appId")
    public void createSettlementContract(Long mid, SettlementContractCreateBo contractCreateBo) {

        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);

        AssertUtil.isTrue(accountService.isAdmin(mid, contractCreateBo.getAppId(), company), ErrorCodeType.NOT_ADMIN);

        validateContractTime(contractCreateBo);

        CreateContractResult contractResult = createContract(contractCreateBo, company);

        saveContract(contractCreateBo, contractResult);

        log.info("[ContractSettlementService] 合同结算创建成功, appId={}, contractId={}",
                contractCreateBo.getAppId(), contractResult.getContractId());
    }


    private CreateContractResult createContract(SettlementContractCreateBo request, CompanyDetailBo company) {
        CreateContractRequest contractRequest = buildCreateContractRequest(request, company);
        return contractCenterApiService.createContract(contractRequest);
    }

    /**
     * 校验合同时间参数
     */
    private void validateContractTime(SettlementContractCreateBo request) {
        Long startTime = request.getContractStartTime();
        Long endTime = request.getContractEndTime();

        AssertUtil.isTrue(startTime > 0 && endTime > 0,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同时间参数不正确");

        AssertUtil.isTrue(endTime > startTime,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同结束时间必须大于开始时间");
    }

    /**
     * 构建合同创建请求
     */
    private CreateContractRequest buildCreateContractRequest(SettlementContractCreateBo request, CompanyDetailBo company) {

        ContractEntity contractEntity = ContractEntity.builder()
                .entityPartner("乙方")
                .entityType(ContractCenterEntityType.COMPANY.getCode())
                .entityName(company.getCompanyInfo().getCompanyName())
                .build();

        return CreateContractRequest.builder()
                .business("小程序结算")
                .entity(List.of("机构"))
                .contractType(ContractCenterContractType.COMPANY.getCode())
                .entities(Collections.singletonList(contractEntity))
                .oaInfo(new OaInfo())
                .requiredTplId(configCenter.getContractConfig().getContractTemplateId())
                .build();
    }

    private void saveContract(SettlementContractCreateBo request, CreateContractResult contractResult) {

        MiniAppOpenContractPo contractPo = MiniAppOpenContractPo.builder()
                .appId(request.getAppId())
                .signatoryName(request.getSignatoryName())
                .signatoryPhone(request.getSignatoryPhone())
                .signatoryEmail(request.getSignatoryEmail())
                .contactAddress(request.getContactAddress())
                .contractStartTime(new Timestamp(request.getContractStartTime()))
                .contractEndTime(new Timestamp(request.getContractEndTime()))
                .contractId(contractResult.getContractId())
                .contractStatus(contractResult.getState())
                .build();

        miniAppOpenContractDao.insert(contractPo);
    }
}

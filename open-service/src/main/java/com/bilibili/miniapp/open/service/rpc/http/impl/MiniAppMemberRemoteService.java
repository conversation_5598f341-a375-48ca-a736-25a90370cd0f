package com.bilibili.miniapp.open.service.rpc.http.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppMemberDTO;
import com.bilibili.mall.miniapp.query.miniapp.MiniAppMemberQuery;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.rpc.http.IMiniAppMemberRemoteService;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@Component
public class MiniAppMemberRemoteService extends AbstractOpenService {

    @Resource
    private IMiniAppMemberRemoteService memberRemoteService;

    public PageInfo<MiniAppMemberDTO> listMiniAppMember(MiniAppMemberQuery miniAppMemberQuery) {
        return call("获取指定成员的小程序信息(非管理员)",
                memberRemoteService::listMiniAppMember,
                miniAppMemberQuery);
    }

}

package com.bilibili.miniapp.open.service.biz.finance.impl;

import com.bilibili.miniapp.open.common.enums.InvoiceItemCategory;
import com.bilibili.miniapp.open.common.enums.InvoiceType;
import com.bilibili.miniapp.open.common.enums.TaxType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenFinanceInfoDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenFinanceInfoPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.BankInfoBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailRespBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceSaveReqBo;
import com.bilibili.miniapp.open.service.bo.finance.InvoiceInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 财务服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class FinanceService implements IFinanceService {

    @Autowired
    private MiniAppOpenFinanceInfoDao financeInfoDao;

    @Autowired
    private ICompanyService companyService;

    @Override
    @LockRequest(key = "'mp_open_lock_create_finance_' + #mid")
    public void saveFinanceInfo(Long mid, FinanceSaveReqBo financeSaveReqBo) {

        TaxType.getByCode(financeSaveReqBo.getInvoiceInfo().getTaxType());
        InvoiceType.getByCode(financeSaveReqBo.getInvoiceInfo().getInvoiceType());
        InvoiceItemCategory.getByCode(financeSaveReqBo.getInvoiceInfo().getInvoiceItemCategory());

        String companyId = getCompanyId(mid);

        MiniAppOpenFinanceInfoPo existing = getByCompanyId(companyId);
        if (existing == null) {
            MiniAppOpenFinanceInfoPo po = MiniAppOpenFinanceInfoPo.builder()
                    .companyId(companyId)
                    .bankAccountNumber(financeSaveReqBo.getBankInfo().getBankAccountNumber())
                    .bankName(financeSaveReqBo.getBankInfo().getBankName())
                    .bankBranchName(financeSaveReqBo.getBankInfo().getBankBranchName())
                    .taxType(financeSaveReqBo.getInvoiceInfo().getTaxType())
                    .invoiceType(financeSaveReqBo.getInvoiceInfo().getInvoiceType())
                    .invoiceItemCategory(financeSaveReqBo.getInvoiceInfo().getInvoiceItemCategory())
                    .build();
            financeInfoDao.insertSelective(po);
        } else {
            existing.setBankAccountNumber(financeSaveReqBo.getBankInfo().getBankAccountNumber());
            existing.setBankName(financeSaveReqBo.getBankInfo().getBankName());
            existing.setBankBranchName(financeSaveReqBo.getBankInfo().getBankBranchName());
            existing.setInvoiceItemCategory(financeSaveReqBo.getInvoiceInfo().getInvoiceItemCategory());

            financeInfoDao.updateByPrimaryKeySelective(existing);
        }
    }

    private String getCompanyId(Long mid) {
        CompanyDetailBo companyDetail = companyService.getCreatedCompanyDetail(mid);
        if (companyDetail == null || companyDetail.getCompanyInfo() == null) {
            throw new ServiceException("用户无权限创建财务信息");
        }

        return companyDetail.getCompanyInfo().getCompanyId();
    }

    /**
     * 根据公司ID查询财务信息
     */
    private MiniAppOpenFinanceInfoPo getByCompanyId(String companyId) {
        MiniAppOpenFinanceInfoPoExample example = new MiniAppOpenFinanceInfoPoExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenFinanceInfoPo> list = financeInfoDao.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }


    @Override
    public FinanceDetailRespBo getFinanceDetail(Long mid) {

        String companyId = getCompanyId(mid);

        MiniAppOpenFinanceInfoPo financeInfo = getByCompanyId(companyId);
        if (financeInfo == null) {
            return FinanceDetailRespBo.builder().build();
        }

        return FinanceDetailRespBo.builder()
                .bankInfo(BankInfoBo.builder()
                        .bankAccountNumber(financeInfo.getBankAccountNumber())
                        .bankName(financeInfo.getBankName())
                        .bankBranchName(financeInfo.getBankBranchName())
                        .build())
                .invoiceInfo(InvoiceInfoBo.builder()
                        .taxType(financeInfo.getTaxType())
                        .invoiceType(financeInfo.getInvoiceType())
                        .invoiceItemCategory(financeInfo.getInvoiceItemCategory())
                        .build())
                .build();
    }
}

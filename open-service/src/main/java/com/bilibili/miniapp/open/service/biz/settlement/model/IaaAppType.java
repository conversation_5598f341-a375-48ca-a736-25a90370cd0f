package com.bilibili.miniapp.open.service.biz.settlement.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * iaa收入来源
 *
 * <AUTHOR>
 * @desc
 * @date 2025/3/17
 */
@Getter
@RequiredArgsConstructor
public enum IaaAppType {

    unknown(0, "未知"),

    mini_game(1, "小游戏"),

    mini_app(2, "小程序")


    ;


    private final int code;

    private final String desc;


    public static IaaAppType of(Integer code) {
        for (IaaAppType value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return unknown;
    }

    public static IaaAppType fromName(String name) {
        for (IaaAppType value : values()) {
            if (value.name().equalsIgnoreCase(name)) {
                return value;
            }
        }
        return unknown;
    }

}

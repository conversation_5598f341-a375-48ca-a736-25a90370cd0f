package com.bilibili.miniapp.open.service.biz.contract;

import com.bilibili.miniapp.open.portal.vo.contract.ContractSettlementCreateReqVo;

/**
 * 合同结算服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IContractSettlementService {
    
    /**
     * 创建合同结算
     *
     * @param mid 用户ID
     * @param request 创建请求
     */
    void createContractSettlement(Long mid, ContractSettlementCreateReqVo request);
}

package com.bilibili.miniapp.open.service.biz.income.impl;

import com.bilibili.mall.miniapp.dto.miniapp.MiniAppDTO;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenIaaIncomeDetailDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenIaaIncomeDetailPoExample;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.income.IIncomeService;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 收入服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class IncomeService implements IIncomeService {


    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Autowired
    private MiniAppOpenIaaIncomeDetailDao iaaIncomeDetailDao;

    @Override
    public IncomeSummaryBo getIncomeSummary(Long mid) {

        AssertUtil.isTrue(accountService.isAdmin(mid, null), ErrorCodeType.NOT_ADMIN);

        List<MiniAppDTO> miniApps = miniAppRemoteService.listMainMiniAppsFromCache(mid);

        if (CollectionUtils.isEmpty(miniApps)) {
            return new IncomeSummaryBo();
        }

        List<String> appIds = miniApps.stream().map(MiniAppDTO::getAppId).collect(Collectors.toList());

        return calculateIncomeSummary(appIds);
    }

    private IncomeSummaryBo calculateIncomeSummary(List<String> appIds) {

        MiniAppOpenIaaIncomeDetailPoExample example = new MiniAppOpenIaaIncomeDetailPoExample();
        example.createCriteria()
                .andAppIdIn(appIds)
                .andWithdrawStatusIn(List.of(WithdrawStatus.WITHDRAWABLE.getCode(),WithdrawStatus.WITHDRAWABLE.getCode()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenIaaIncomeDetailPo> incomeDetails = iaaIncomeDetailDao.selectByExample(example);

        if (CollectionUtils.isEmpty(incomeDetails)) {
            return new IncomeSummaryBo();
        }

        Map<Integer, List<MiniAppOpenIaaIncomeDetailPo>> statusMap = incomeDetails.stream().collect(Collectors.groupingBy(MiniAppOpenIaaIncomeDetailPo::getWithdrawStatus));

        BigDecimal withdrawableAmount = calculateAmount(statusMap.get(WithdrawStatus.WITHDRAWABLE.getCode()));
        BigDecimal withdrawingAmount = calculateAmount(statusMap.get(WithdrawStatus.WITHDRAWING.getCode()));

        return IncomeSummaryBo.builder()
                .withdrawableAmount(withdrawableAmount)
                .withdrawingAmount(withdrawingAmount)
                .build();
    }

    private BigDecimal calculateAmount(List<MiniAppOpenIaaIncomeDetailPo> incomeDetailPos) {

        return incomeDetailPos.stream()
                .map(MiniAppOpenIaaIncomeDetailPo::getActualIncomeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}

package com.bilibili.miniapp.open.service.bo.contract;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合同结算创建请求BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementContractCreateBo {
    
    /**
     * 小程序ID
     */
    private String appId;
    
    /**
     * 签约人姓名
     */
    private String signatoryName;
    
    /**
     * 签约人手机号
     */
    private String signatoryPhone;
    
    /**
     * 签约人电子邮箱
     */
    private String signatoryEmail;
    
    /**
     * 联系地址
     */
    private String contactAddress;
    
    /**
     * 合同生效开始时间
     */
    private Long contractStartTime;
    
    /**
     * 合同生效结束时间
     */
    private Long contractEndTime;
}

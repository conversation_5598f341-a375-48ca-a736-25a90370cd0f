package com.bilibili.miniapp.open.service.biz.contract.impl;

import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.common.exception.ErrorCodeType;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.portal.vo.contract.ContractSettlementCreateReqVo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.contract.IContractSettlementService;
import com.bilibili.miniapp.open.service.rpc.http.dto.*;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;

/**
 * 合同结算服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class ContractSettlementService implements IContractSettlementService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private ContractCenterApiService contractCenterApiService;

    @Autowired
    private MiniAppOpenContractDao miniAppOpenContractDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createContractSettlement(Long mid, ContractSettlementCreateReqVo request) {
        // 1. 校验用户是否有超管权限
        validateSuperAdminPermission(mid, request.getAppId());

        // 2. 校验时间参数
        validateContractTime(request);

        // 3. 构建合同创建请求
        CreateContractRequest contractRequest = buildCreateContractRequest(request);

        // 4. 调用合同中心API创建合同
        CreateContractResult contractResult = contractCenterApiService.createContract(contractRequest);

        // 5. 保存合同信息到数据库
        saveContractToDatabase(request, contractResult);

        log.info("[ContractSettlementService] 合同结算创建成功, appId={}, contractId={}", 
                request.getAppId(), contractResult.getContractId());
    }

    /**
     * 校验用户是否有超管权限
     */
    private void validateSuperAdminPermission(Long mid, String appId) {
        Integer userRole = accountService.getUserRole(mid, appId);
        AssertUtil.isTrue(MiniAppPermission.SUPER_ADMIN.getCode() == userRole,
                ErrorCodeType.UNAUTHORIZED.getCode(), "无超管权限，无法创建合同");
    }

    /**
     * 校验合同时间参数
     */
    private void validateContractTime(ContractSettlementCreateReqVo request) {
        Long startTime = request.getContractStartTime();
        Long endTime = request.getContractEndTime();
        
        AssertUtil.isTrue(startTime > 0 && endTime > 0,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同时间参数不正确");
        
        AssertUtil.isTrue(endTime > startTime,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同结束时间必须大于开始时间");
    }

    /**
     * 构建合同创建请求
     */
    private CreateContractRequest buildCreateContractRequest(ContractSettlementCreateReqVo request) {
        // 构建签约方实体
        ContractEntity contractEntity = ContractEntity.builder()
                .entityPartner("乙方")
                .entityType(1) // 个人
                .entityName(request.getSignatoryName())
                .notifyEmail(request.getSignatoryEmail())
                .notifyMobile(request.getSignatoryPhone())
                .isFiller(1)
                .build();

        // 构建OA审核信息
        OaInfo oaInfo = OaInfo.builder()
                .title("小程序结算合同")
                .projectName("小程序结算")
                .startTime(request.getContractStartTime())
                .endTime(request.getContractEndTime())
                .contractFullName("小程序结算合同")
                .isFrameworkContract(0)
                .oaContractType(0L) // 起草合同
                .build();

        return CreateContractRequest.builder()
                .business("小程序结算")
                .entity(Arrays.asList("供应商"))
                .contractType(1L) // 个人
                .entities(Collections.singletonList(contractEntity))
                .oaInfo(oaInfo)
                .requiredTplId("miniapp_settlement_template") // 小程序结算模板ID
                .operator("system")
                .bizType(1)
                .initiator(1L) // 甲方发起
                .fillType(1L) // 无代填
                .signModus(1L) // 线上签约
                .isInOa(1L) // 在OA归档
                .tplContractType(1L) // 原合同
                .signTimeLimit(30L) // 30天签约时限
                .thirdSignPlatform(1L) // e签宝
                .build();
    }

    /**
     * 保存合同信息到数据库
     */
    private void saveContractToDatabase(ContractSettlementCreateReqVo request, CreateContractResult contractResult) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        
        MiniAppOpenContractPo contractPo = MiniAppOpenContractPo.builder()
                .appId(request.getAppId())
                .signatoryName(request.getSignatoryName())
                .signatoryPhone(request.getSignatoryPhone())
                .signatoryEmail(request.getSignatoryEmail())
                .contactAddress(request.getContactAddress())
                .contractStartTime(new Timestamp(request.getContractStartTime() * 1000))
                .contractEndTime(new Timestamp(request.getContractEndTime() * 1000))
                .contractId(contractResult.getContractId())
                .contractStatus(contractResult.getState() != null ? contractResult.getState() : 0)
                .isDeleted(0)
                .ctime(now)
                .mtime(now)
                .build();

        int result = miniAppOpenContractDao.insert(contractPo);
        if (result <= 0) {
            throw new ServiceException(ErrorCodeType.SYSTEM_ERROR.getCode(), "保存合同信息失败");
        }
    }
}

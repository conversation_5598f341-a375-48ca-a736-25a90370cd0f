package com.bilibili.miniapp.open.portal.vo.contract;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 合同结算创建请求VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementContractCreateVo {
    
    /**
     * 小程序ID
     */
    @NotBlank(message = "小程序ID不能为空")
    private String appId;
    
    /**
     * 签约人姓名
     */
    @NotBlank(message = "签约人姓名不能为空")
    private String signatoryName;
    
    /**
     * 签约人手机号
     */
    @NotBlank(message = "签约人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String signatoryPhone;
    
    /**
     * 签约人电子邮箱
     */
    @NotBlank(message = "签约人电子邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String signatoryEmail;
    
    /**
     * 联系地址
     */
    @NotBlank(message = "联系地址不能为空")
    private String contactAddress;
    
    /**
     * 合同生效开始时间（时间戳）
     */
    @NotNull(message = "合同生效开始时间不能为空")
    private Long contractStartTime;
    
    /**
     * 合同生效结束时间（时间戳）
     */
    @NotNull(message = "合同生效结束时间不能为空")
    private Long contractEndTime;
}

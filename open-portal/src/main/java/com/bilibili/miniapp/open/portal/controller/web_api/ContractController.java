package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.vo.contract.ContractSettlementCreateReqVo;
import com.bilibili.miniapp.open.service.biz.contract.IContractSettlementService;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 合同相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/contract")
public class ContractController extends AbstractController {

    @Autowired
    private IContractSettlementService contractSettlementService;

    /**
     * 创建合同结算
     */
    @PostMapping("/settlement/create")
    @MainSiteLoginValidation
    public Response<Void> createContractSettlement(Context context,
                                                   @Valid @RequestBody ContractSettlementCreateReqVo request) {
        contractSettlementService.createContractSettlement(context.getMid(), request);
        return Response.SUCCESS();
    }
}

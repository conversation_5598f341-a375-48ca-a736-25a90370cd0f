package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.income.IncomeSummaryVo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 收入控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface IncomeControllerMapper extends BaseAmountMapper {


    IncomeControllerMapper MAPPER = Mappers.getMapper(IncomeControllerMapper.class);

    /**
     * 收入汇总BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawableAmount", expression = "java(convertCentToYuan(bo.getWithdrawableAmount()))")
    @Mapping(target = "withdrawingAmount", expression = "java(convertCentToYuan(bo.getWithdrawingAmount()))")
    IncomeSummaryVo boToVo(IncomeSummaryBo bo);


}

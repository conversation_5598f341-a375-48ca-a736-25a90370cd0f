package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.contract.SettlementContractCreateVo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractCreateBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 合同控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface ContractControllerMapper {

    ContractControllerMapper MAPPER = Mappers.getMapper(ContractControllerMapper.class);

    /**
     * 创建请求VO转BO
     */
    SettlementContractCreateBo voToBo(SettlementContractCreateVo vo);
}
